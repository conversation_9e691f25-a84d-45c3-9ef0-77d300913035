const AppService = require('./AppService');
const { JobVacancy, JobTitle } = require('../models');
const JobVacanciesRepository = require('../repositories/JobVacanciesRepository');

class JobVacancyService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacanciesRepository();
  }

  /**
   * Get all job vacancies
   * @param {Object} params - Query params (page, limit, filters, etc.)
   * @returns {Object} Job vacancies array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_vacancies: rows, pagination };
  }

  /**
   * Find a job vacancy by ID
   * @param {number} id - Job vacancy ID
   * @returns {Object} Job vacancy object
   * @throws {NotFoundError} If job vacancy is not found
   */
  async findById(id) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');
    return vacancy;
  }

  /**
   * Create a new job vacancy
   * @param {Object} data - Job vacancy data
   * @returns {Object} Created job vacancy
   * @throws {NotFoundError} If job title is not found
   */
  async create(data) {
    const jobTitle = await JobTitle.findByPk(data.job_title_id);
    this.exists(jobTitle, 'Job title not found');

    const vacancyData = {
      ...data,
      name: jobTitle.name,
    };

    const vacancy = await JobVacancy.create(vacancyData);

    // mock data for now
    await this.injectMockData(vacancy);

    return vacancy;
  }

  // async injectMockData(vacancy) {
  //   const mockData = [
  //     [
  //       {
  //         id: 1,
  //         user: {
  //           id: 101,
  //           name: 'Sarah Johnson',
  //           current_position: {
  //             role_name: 'Marketing Manager',
  //             department: 'Marketing',
  //             job_grade: 'Manager',
  //           },
  //         },
  //         competency_match: 0.85,
  //         skill_match: 0.78,
  //       },
  //       {
  //         id: 2,
  //         user: {
  //           id: 102,
  //           name: 'Michael Chen',
  //           current_position: {
  //             role_name: 'Product Manager',
  //             department: 'Product Development',
  //             job_grade: 'Senior Manager',
  //           },
  //         },
  //         competency_match: 0.72,
  //         skill_match: 0.85,
  //       },
  //       {
  //         id: 3,
  //         user: {
  //           id: 103,
  //           name: 'Emily Rodriguez',
  //           current_position: {
  //             role_name: 'Digital Marketing Specialist',
  //             department: 'Marketing',
  //             job_grade: 'Specialist',
  //           },
  //         },
  //         competency_match: 0.68,
  //         skill_match: 0.92,
  //       },
  //       {
  //         id: 4,
  //         user: {
  //           id: 104,
  //           name: 'David Kim',
  //           current_position: {
  //             role_name: 'Brand Coordinator',
  //             department: 'Marketing',
  //             job_grade: 'Coordinator',
  //           },
  //         },
  //         competency_match: 0.75,
  //         skill_match: 0.65,
  //       },
  //       {
  //         id: 5,
  //         user: {
  //           id: 105,
  //           name: 'Lisa Wang',
  //           current_position: {
  //             role_name: 'Marketing Analyst',
  //             department: 'Marketing',
  //             job_grade: 'Analyst',
  //           },
  //         },
  //         competency_match: 0.58,
  //         skill_match: 0.71,
  //       },
  //     ],
  //     [
  //       {
  //         id: 6,
  //         user: {
  //           id: 201,
  //           name: 'Robert Anderson',
  //           current_position: {
  //             role_name: 'Operations Manager',
  //             department: 'Operations',
  //             job_grade: 'Manager',
  //           },
  //         },
  //         competency_match: 0.88,
  //         skill_match: 0.82,
  //       },
  //       {
  //         id: 7,
  //         user: {
  //           id: 202,
  //           name: 'Jennifer Lee',
  //           current_position: {
  //             role_name: 'Supply Chain Manager',
  //             department: 'Operations',
  //             job_grade: 'Manager',
  //           },
  //         },
  //         competency_match: 0.91,
  //         skill_match: 0.89,
  //       },
  //       {
  //         id: 8,
  //         user: {
  //           id: 203,
  //           name: 'Mark Thompson',
  //           current_position: {
  //             role_name: 'Logistics Coordinator',
  //             department: 'Operations',
  //             job_grade: 'Coordinator',
  //           },
  //         },
  //         competency_match: 0.65,
  //         skill_match: 0.78,
  //       },
  //     ],
  //   ];

  //   const randomIndex = Math.floor(Math.random() * mockData.length);
  //   const randomData = mockData[randomIndex];

  //   // await vacancy.set('applications', randomData, { through: { status: 'applied' } });
  //   // await vacancy.save();
  // }
}

module.exports = JobVacancyService;
